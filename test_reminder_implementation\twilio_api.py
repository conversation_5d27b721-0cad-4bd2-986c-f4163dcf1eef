# test_reminder_implementation/twilio_api.py

import os
import re
from dotenv import load_dotenv
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

# Load environment variables from the parent directory's .env file
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env'))

TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
TWILIO_NUMBER = os.getenv("TWILIO_WHATSAPP_NUMBER")

# Test mode flag - set to True to prevent actual message sending during testing
TEST_MODE = False  # Change to False to send real messages

def parse_singapore_phone(phone_input):
    """
    Parse and format Singapore phone numbers for WhatsApp.
    
    Handles formats:
    - "1234 5678" -> "+65********"
    - "********" -> "+65********"
    - "+65********" -> "+65********"
    - "65********" -> "+65********"
    
    Returns:
        str: Formatted phone number with +65 prefix, or None if invalid
    """
    if not phone_input:
        return None
    
    # Clean the input: remove spaces, dashes, parentheses
    cleaned = re.sub(r'[\s\-\(\)]', '', str(phone_input))
    
    # Remove any leading + or 00
    if cleaned.startswith('+'):
        cleaned = cleaned[1:]
    elif cleaned.startswith('00'):
        cleaned = cleaned[2:]
    
    # Handle Singapore numbers
    if cleaned.startswith('65'):
        # Already has country code
        if len(cleaned) == 10:  # 65 + 8 digits
            return f"+{cleaned}"
    elif len(cleaned) == 8 and cleaned.isdigit():
        # Local Singapore number (8 digits)
        return f"+65{cleaned}"
    
    # If it doesn't match expected patterns, return None
    return None

def send_whatsapp_reminder(customer_name, appointment_datetime, customer_phone, target_date=None):
    """
    Sends a WhatsApp reminder using Twilio for a specific appointment date.
    
    Args:
        customer_name (str): The name of the customer
        appointment_datetime (datetime): The appointment datetime
        customer_phone (str): The customer's phone number
        target_date (datetime.date, optional): The target date for the appointment
    """
    if not all([TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_NUMBER]):
        print("    Error: Twilio credentials are not fully configured.")
        print("    Please check TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, and TWILIO_WHATSAPP_NUMBER in .env")
        return

    # Parse and validate phone number
    formatted_phone = parse_singapore_phone(customer_phone)
    if not formatted_phone:
        print(f"    Error: Invalid phone number format for {customer_name}: {customer_phone}")
        return

    # Ensure appointment_datetime is a datetime object
    if isinstance(appointment_datetime, str):
        from dateutil import parser
        try:
            appointment_datetime = parser.parse(appointment_datetime)
        except Exception:
            print(f"    Error: Could not parse appointment time for {customer_name}: {appointment_datetime}")
            return

    try:
        # Format the appointment time
        appointment_time = appointment_datetime.strftime('%I:%M %p')  # e.g., "03:30 PM"
        
        # Create a more specific message for the test date
        if target_date:
            date_str = target_date.strftime('%A, %B %d, %Y')  # e.g., "Friday, September 12, 2025"
            message_body = (
                f"Hi {customer_name}, this is a reminder for your "
                f"appointment on {date_str} at {appointment_time}. See you soon!"
            )
        else:
            # Fallback to generic message
            message_body = (
                f"Hi {customer_name}, this is a reminder for your "
                f"appointment at {appointment_time}. See you soon!"
            )

        if TEST_MODE:
            print(f"    [TEST MODE] Would send WhatsApp message:")
            print(f"    To: {formatted_phone}")
            print(f"    From: {TWILIO_NUMBER}")
            print(f"    Message: {message_body}")
            print(f"    [TEST MODE] Message not actually sent")
        else:
            # Initialize Twilio client and send the message
            client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

            message = client.messages.create(
                from_=f'whatsapp:{TWILIO_NUMBER}',
                body=message_body,
                to=f'whatsapp:{formatted_phone}'
            )
            
            print(f"    Successfully sent reminder to {customer_name} at {formatted_phone} (SID: {message.sid})")

    except TwilioRestException as e:
        print(f"    Twilio error sending message to {customer_name}: {e}")
    except Exception as e:
        print(f"    Unexpected error sending message to {customer_name}: {e}")

def toggle_test_mode(enabled=True):
    """
    Toggle test mode on or off.
    
    Args:
        enabled (bool): True to enable test mode (no actual messages sent), 
                       False to disable test mode (real messages will be sent)
    """
    global TEST_MODE
    TEST_MODE = enabled
    mode_status = "ENABLED" if enabled else "DISABLED"
    print(f"Test mode {mode_status}. Real messages will {'NOT' if enabled else ''} be sent.")

def get_test_mode_status():
    """
    Returns the current test mode status.
    
    Returns:
        bool: True if test mode is enabled, False otherwise
    """
    return TEST_MODE
