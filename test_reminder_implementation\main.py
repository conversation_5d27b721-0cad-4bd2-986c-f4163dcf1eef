# test_reminder_implementation/main.py

import datetime
from supersaas_api import get_appointments_for_day
from twilio_api import send_whatsapp_reminder

def main():
    """
    Test implementation to fetch appointments for Friday, September 12th, 2025
    and send reminders for that specific date.
    """
    print("=" * 60)
    print("TEST REMINDER IMPLEMENTATION")
    print("Target Date: Friday, September 12th, 2025")
    print("=" * 60)

    # Hardcoded target date: Friday, September 12th, 2025
    target_date = datetime.date(2025, 9, 12)
    
    print(f"Fetching appointments for {target_date.strftime('%A, %B %d, %Y')}...")
    
    # Fetch appointments for the specific date
    appointments_to_remind = get_appointments_for_day(target_date)

    # Process and send reminders
    if not appointments_to_remind:
        print(f"No appointments found for {target_date.strftime('%A, %B %d, %Y')}.")
        print("This could mean:")
        print("  - No appointments are scheduled for that date")
        print("  - The appointments don't have the required information (name/phone)")
        print("  - There was an API error")
    else:
        print(f"Found {len(appointments_to_remind)} appointment(s) for {target_date.strftime('%A, %B %d, %Y')}.")
        print("\nProcessing appointments and sending reminders...")
        print("-" * 40)
        
        successful_reminders = 0
        skipped_appointments = 0
        
        for i, appt in enumerate(appointments_to_remind, 1):
            print(f"\nAppointment {i}/{len(appointments_to_remind)}:")
            print(f"  ID: {getattr(appt, 'id', 'N/A')}")
            print(f"  Name: {getattr(appt, 'full_name', 'N/A')}")
            print(f"  Time: {appt.start.strftime('%I:%M %p') if hasattr(appt, 'start') and appt.start else 'N/A'}")
            print(f"  Phone: {getattr(appt, 'phone', 'N/A')}")
            
            # Ensure the customer phone number and name exist
            if hasattr(appt, 'phone') and appt.phone and hasattr(appt, 'full_name') and appt.full_name:
                try:
                    send_whatsapp_reminder(
                        customer_name=appt.full_name,
                        appointment_datetime=appt.start,
                        customer_phone=appt.phone,
                        target_date=target_date  # Pass the specific date for message customization
                    )
                    successful_reminders += 1
                    print(f"  Status: ✓ Reminder sent successfully")
                except Exception as e:
                    print(f"  Status: ✗ Error sending reminder: {e}")
                    skipped_appointments += 1
            else:
                missing_info = []
                if not hasattr(appt, 'full_name') or not appt.full_name:
                    missing_info.append("name")
                if not hasattr(appt, 'phone') or not appt.phone:
                    missing_info.append("phone")
                
                print(f"  Status: ✗ Skipped - Missing {' and '.join(missing_info)}")
                skipped_appointments += 1
        
        print("\n" + "=" * 60)
        print("SUMMARY:")
        print(f"  Total appointments found: {len(appointments_to_remind)}")
        print(f"  Successful reminders sent: {successful_reminders}")
        print(f"  Skipped appointments: {skipped_appointments}")
        print("=" * 60)
    
    print("\nTest reminder implementation completed.")

if __name__ == "__main__":
    main()
