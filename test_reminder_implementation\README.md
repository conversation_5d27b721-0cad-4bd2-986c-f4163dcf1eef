# Test Reminder Implementation

This folder contains a test implementation for sending appointment reminders for **Friday, September 12th, 2025**.

## Overview

This test implementation demonstrates how the appointment reminder system works for a specific date without modifying any existing files in the main codebase.

## Files

- **`main.py`**: Main script that orchestrates the reminder process for September 12th, 2025
- **`supersaas_api.py`**: SuperSaaS API integration for fetching appointments on the target date
- **`twilio_api.py`**: Twilio WhatsApp integration for sending reminder messages
- **`README.md`**: This documentation file

## Features

### Main Script (`main.py`)
- Hardcoded target date: Friday, September 12th, 2025
- Fetches appointments for the specific date
- Processes each appointment and sends reminders
- Provides detailed logging and summary statistics
- Shows appointment details (ID, name, time, phone)

### SuperSaaS API (`supersaas_api.py`)
- Fetches appointments from SuperSaaS for the target date
- Extracts phone numbers from various fields (mobile, phone, custom fields)
- Handles date parsing and filtering
- Robust error handling for API issues

### Twilio API (`twilio_api.py`)
- Sends WhatsApp reminders via Twilio
- Formats Singapore phone numbers correctly (+65 prefix)
- **TEST MODE ENABLED**: By default, messages are not actually sent
- Customized message mentioning the specific date
- Error handling for invalid phone numbers and Twilio issues

## Safety Features

### Test Mode
By default, the Twilio integration runs in **TEST MODE**, which means:
- No actual WhatsApp messages are sent
- All message details are logged to the console
- You can see exactly what would be sent without sending real messages

To disable test mode and send real messages:
1. Open `twilio_api.py`
2. Change `TEST_MODE = True` to `TEST_MODE = False` on line 15
3. **WARNING**: This will send real WhatsApp messages to customers!

## Prerequisites

1. **Environment Variables**: The script uses the same `.env` file from the parent directory with:
   - `SSS_API_ACCOUNT_NAME`: SuperSaaS account name
   - `SSS_API_KEY`: SuperSaaS API key
   - `TWILIO_ACCOUNT_SID`: Twilio account SID
   - `TWILIO_AUTH_TOKEN`: Twilio auth token
   - `TWILIO_WHATSAPP_NUMBER`: Twilio WhatsApp number

2. **Python Dependencies**: Same as the main project:
   - `python-dotenv`
   - `SuperSaaS`
   - `twilio`
   - `python-dateutil`

## How to Run

1. **Navigate to the test folder**:
   ```bash
   cd test_reminder_implementation
   ```

2. **Run the test script**:
   ```bash
   python main.py
   ```

3. **Review the output**:
   - The script will show all appointments found for September 12th, 2025
   - In TEST MODE, it will show what messages would be sent
   - A summary will display total appointments, successful reminders, and skipped appointments

## Expected Output

```
============================================================
TEST REMINDER IMPLEMENTATION
Target Date: Friday, September 12th, 2025
============================================================
Fetching appointments for Friday, September 12, 2025...
Initializing SuperSaaS client...
Fetching appointments for 2025-09-12...
Time range: 2025-09-12T00:00:00 to 2025-09-13T00:00:00
API returned X appointment(s). Filtering for target date...
Found X appointment(s) on 2025-09-12

Processing appointments and sending reminders...
----------------------------------------

Appointment 1/X:
  ID: 12345
  Name: John Doe
  Time: 10:30 AM
  Phone: +6512345678
    [TEST MODE] Would send WhatsApp message:
    To: +6512345678
    From: +14155238886
    Message: Hi John Doe, this is a reminder for your appointment on Friday, September 12, 2025 at 10:30 AM. See you soon!
    [TEST MODE] Message not actually sent
  Status: ✓ Reminder sent successfully

============================================================
SUMMARY:
  Total appointments found: X
  Successful reminders sent: X
  Skipped appointments: X
============================================================

Test reminder implementation completed.
```

## Customization

- **Change target date**: Modify the `target_date` variable in `main.py`
- **Enable real messaging**: Set `TEST_MODE = False` in `twilio_api.py`
- **Modify message content**: Edit the message template in `twilio_api.py`
- **Adjust schedule ID**: Change `SCHEDULE_ID` in `supersaas_api.py` if needed

## Notes

- This implementation is completely separate from the main codebase
- No existing files are modified
- The test uses the same credentials and configuration as the main system
- All logging is designed to be clear and informative for testing purposes
