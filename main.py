# main.py

import datetime
from supersaas_api import get_appointments_for_day
from twilio_api import send_whatsapp_reminder

def main():
    """
    Main function to fetch tomorrow's appointments and send reminders.
    """
    print("--- Starting Daily Reminder Script ---")

    # 1. Determine the target date (tomorrow)
    tomorrow = datetime.date.today() + datetime.timedelta(days=1)
    
    # 2. Fetch appointments for that date
    appointments_to_remind = get_appointments_for_day(tomorrow)

    # 3. Process and send reminders
    if not appointments_to_remind:
        print(f"No appointments found for tomorrow, {tomorrow.strftime('%Y-%m-%d')}.")
    else:
        print(f"Found {len(appointments_to_remind)} appointments. Sending reminders...")
        for appt in appointments_to_remind:
            # Ensure the customer phone number and name exist
            if appt.phone and appt.full_name:
                send_whatsapp_reminder(
                    customer_name=appt.full_name,
                    appointment_datetime=appt.start,
                    customer_phone=appt.phone
                )
            else:
                print(f"  - Skipping appointment ID {appt.id}: Missing name or phone number.")
    
    print("--- Daily <PERSON>mind<PERSON> Finished ---")

if __name__ == "__main__":
    main()