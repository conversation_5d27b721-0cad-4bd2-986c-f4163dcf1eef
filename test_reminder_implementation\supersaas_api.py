# test_reminder_implementation/supersaas_api.py

import datetime
import os
from dotenv import load_dotenv
from dateutil import parser
from SuperSaaS import Client, Error

# Load environment variables from the parent directory's .env file
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env'))

SCHEDULE_ID = 792729  # SuperSaaS schedule ID
FETCH_LIMIT = 200     # Maximum number of appointments to fetch

def safe_parse_datetime(value):
    """
    Converts value to datetime.datetime, or returns None if impossible.
    """
    if value is None:
        return None
    if isinstance(value, datetime.datetime):
        return value
    try:
        return parser.parse(value)
    except Exception:
        return None

def extract_phone_from_appt(appt_dict):
    """
    Attempts to extract a phone number from the appointment dictionary.
    Searches in multiple common keys: 'mobile', 'phone', 'telephone', etc.
    """
    if not isinstance(appt_dict, dict):
        return None

    # Probable keys (sequential search)
    candidates = [
        "mobile",
        "phone",
        "telephone",
        "tel",
        "contact",
    ]

    for key in candidates:
        val = appt_dict.get(key)
        if val:
            # Avoid empty strings
            if isinstance(val, str) and val.strip():
                return val.strip()
            if val is not None:
                return val

    # Sometimes custom fields might be under 'custom' or 'fields'
    # We also try to scan these objects if present
    for container_key in ("custom", "fields", "answers"):
        container = appt_dict.get(container_key)
        if isinstance(container, dict):
            for subk, subv in container.items():
                if isinstance(subv, str) and subv.strip():
                    # Heuristic: if the value looks like a number (digits + +)
                    if any(ch.isdigit() for ch in subv):
                        return subv.strip()
    return None

def get_appointments_for_day(target_date):
    """
    Fetches all appointments from SuperSaaS for a specific date.
    
    Args:
        target_date (datetime.date): The date to fetch appointments for
        
    Returns:
        list: List of appointment objects with parsed datetime and phone attributes
    """
    print(f"Initializing SuperSaaS client...")
    
    try:
        client = Client.instance()
    except Exception as e:
        print(f"Error initializing SuperSaaS client: {e}")
        print("Please check your SSS_API_ACCOUNT_NAME and SSS_API_KEY in the .env file")
        return []
    
    # Calculate the time range for the target day
    target_start = datetime.datetime.combine(target_date, datetime.time.min)
    day_after_start = target_start + datetime.timedelta(days=1)

    print(f"Fetching appointments for {target_date.strftime('%Y-%m-%d')}...")
    print(f"Time range: {target_start.isoformat()} to {day_after_start.isoformat()}")

    try:
        # Request appointments starting from target_start
        appointments = client.appointments.list(
            schedule_id=SCHEDULE_ID,
            start_time=target_start,
            limit=FETCH_LIMIT
        )

        if not appointments:
            print("No appointments returned by the SuperSaaS API.")
            return []

        print(f"API returned {len(appointments)} appointment(s). Filtering for target date...")
        target_appointments = []

        for appt in appointments:
            # Safe retrieval of raw fields
            start_raw = getattr(appt, "start", None)
            start_dt = safe_parse_datetime(start_raw)

            # Only consider appointments on the target day if we could parse start_dt
            if not start_dt:
                continue

            if not (target_start <= start_dt < day_after_start):
                # Only take appointments strictly within the target day window
                continue

            # Try to get values directly from object or fallback to raw dict
            try:
                appt_dict = appt.__dict__.copy()
            except Exception:
                appt_dict = {}

            # Extract phone number using multiple methods
            phone = (getattr(appt, "mobile", None) or 
                    getattr(appt, "phone", None) or 
                    extract_phone_from_appt(appt_dict))

            # Add parsed datetime and phone as attributes for easy access
            appt.start = start_dt  # Replace string with parsed datetime
            appt.phone = phone
            target_appointments.append(appt)

        print(f"Found {len(target_appointments)} appointment(s) on {target_date.strftime('%Y-%m-%d')}")
        return target_appointments

    except Error as e:
        print(f"SuperSaaS API error occurred: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error occurred: {e}")
        return []
