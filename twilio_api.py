# twilio_api.py

import os
import re
from dotenv import load_dotenv
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

load_dotenv()

TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
TWILIO_NUMBER = os.getenv("TWILIO_WHATSAPP_NUMBER")

def parse_singapore_phone(phone_input):
    """
    Parse and format Singapore phone numbers for WhatsApp.
    
    Handles formats:
    - "1234 5678" -> "+**********"
    - "********" -> "+**********"
    - "+**********" -> "+**********"
    - "**********" -> "+**********"
    
    Returns:
        str: Formatted phone number with +65 prefix, or None if invalid
    """
    if not phone_input:
        return None
    
    # Clean the input: remove spaces, dashes, parentheses
    cleaned = re.sub(r'[\s\-\(\)]', '', str(phone_input))
    
    # Remove any leading + or 00
    if cleaned.startswith('+'):
        cleaned = cleaned[1:]
    elif cleaned.startswith('00'):
        cleaned = cleaned[2:]
    
    # Handle Singapore numbers
    if cleaned.startswith('65'):
        # Already has country code
        if len(cleaned) == 10:  # 65 + 8 digits
            return f"+{cleaned}"
    elif len(cleaned) == 8 and cleaned.isdigit():
        # Local Singapore number (8 digits)
        return f"+65{cleaned}"
    
    # If it doesn't match expected patterns, return None
    return None

def send_whatsapp_reminder(customer_name, appointment_datetime, customer_phone):
    """Sends a WhatsApp reminder using Twilio."""
    if not all([TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_NUMBER]):
        print("Error: Twilio credentials are not fully configured.")
        return

    # Parse and validate phone number
    formatted_phone = parse_singapore_phone(customer_phone)
    if not formatted_phone:
        print(f"  - Error: Invalid phone number format for {customer_name}: {customer_phone}")
        return

    # Ensure appointment_datetime is a datetime object
    if isinstance(appointment_datetime, str):
        from dateutil import parser
        try:
            appointment_datetime = parser.parse(appointment_datetime)
        except Exception:
            print(f"  - Error: Could not parse appointment time for {customer_name}: {appointment_datetime}")
            return

    try:
        client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

        # Use Windows-compatible time formatting
        appointment_time = appointment_datetime.strftime('%I:%M %p')  # e.g., "03:30 PM"

        message_body = (
            f"Hi {customer_name}, this is a reminder for your "
            f"appointment tomorrow at {appointment_time}. See you soon!"
        )

        message = client.messages.create(
            from_=f'whatsapp:{TWILIO_NUMBER}',
            body=message_body,
            to=f'whatsapp:{formatted_phone}'
        )
        
        print(f"  - Successfully sent reminder to {customer_name} at {formatted_phone} (SID: {message.sid})")

    except TwilioRestException as e:
        print(f"  - Error sending message to {customer_name}: {e}")
