# test_reminder_implementation/test_setup.py

"""
Test setup verification script.
This script checks if all required dependencies and configurations are available.
"""

import os
import sys
from datetime import date, datetime

def check_environment_variables():
    """Check if all required environment variables are set."""
    print("Checking environment variables...")
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv(os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env'))
    except ImportError:
        print("  ✗ python-dotenv not installed")
        return False
    
    required_vars = [
        "SSS_API_ACCOUNT_NAME",
        "SSS_API_KEY", 
        "TWILIO_ACCOUNT_SID",
        "TWILIO_AUTH_TOKEN",
        "TWILIO_WHATSAPP_NUMBER"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"  ✗ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("  ✓ All required environment variables are set")
        return True

def check_dependencies():
    """Check if all required Python packages are installed."""
    print("Checking Python dependencies...")
    
    required_packages = [
        ("dotenv", "python-dotenv"),
        ("SuperSaaS", "SuperSaaS"),
        ("twilio", "twilio"),
        ("dateutil", "python-dateutil")
    ]
    
    missing_packages = []
    for package, pip_name in required_packages:
        try:
            __import__(package)
            print(f"  ✓ {pip_name}")
        except ImportError:
            print(f"  ✗ {pip_name}")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\nTo install missing packages, run:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def test_supersaas_connection():
    """Test SuperSaaS API connection."""
    print("Testing SuperSaaS connection...")
    
    try:
        from SuperSaaS import Client
        client = Client.instance()
        print("  ✓ SuperSaaS client initialized successfully")
        return True
    except Exception as e:
        print(f"  ✗ SuperSaaS connection failed: {e}")
        return False

def test_twilio_connection():
    """Test Twilio connection."""
    print("Testing Twilio connection...")
    
    try:
        from twilio.rest import Client
        account_sid = os.getenv("TWILIO_ACCOUNT_SID")
        auth_token = os.getenv("TWILIO_AUTH_TOKEN")
        
        if not account_sid or not auth_token:
            print("  ✗ Twilio credentials not found")
            return False
            
        client = Client(account_sid, auth_token)
        # Just initialize the client, don't make any API calls
        print("  ✓ Twilio client initialized successfully")
        return True
    except Exception as e:
        print(f"  ✗ Twilio connection failed: {e}")
        return False

def verify_target_date():
    """Verify the target date is correct."""
    print("Verifying target date...")
    
    target_date = date(2025, 9, 12)
    weekday = target_date.strftime('%A')
    
    if weekday == 'Friday':
        print(f"  ✓ Target date {target_date} is a {weekday}")
        return True
    else:
        print(f"  ✗ Target date {target_date} is a {weekday}, not Friday")
        return False

def main():
    """Run all setup verification tests."""
    print("=" * 50)
    print("TEST REMINDER IMPLEMENTATION - SETUP VERIFICATION")
    print("=" * 50)
    
    tests = [
        check_dependencies,
        check_environment_variables,
        test_supersaas_connection,
        test_twilio_connection,
        verify_target_date
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
            print()
        except Exception as e:
            print(f"  ✗ Test failed with error: {e}")
            results.append(False)
            print()
    
    print("=" * 50)
    print("SETUP VERIFICATION SUMMARY")
    print("=" * 50)
    
    if all(results):
        print("✓ All tests passed! The test implementation is ready to run.")
        print("\nTo run the test:")
        print("  python main.py")
        print("\nNote: Test mode is enabled by default (no real messages sent)")
    else:
        print("✗ Some tests failed. Please fix the issues above before running the test.")
        print("\nFailed tests:")
        test_names = [
            "Dependencies",
            "Environment Variables", 
            "SuperSaaS Connection",
            "Twilio Connection",
            "Target Date"
        ]
        for i, (test_name, result) in enumerate(zip(test_names, results)):
            if not result:
                print(f"  - {test_name}")

if __name__ == "__main__":
    main()
