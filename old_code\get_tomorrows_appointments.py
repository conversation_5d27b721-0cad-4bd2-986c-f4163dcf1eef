#!/usr/bin/env python3
"""
get_tomorrows_appointments.py

Récupère les rendez-vous planifiés **pour demain** depuis SuperSaaS et affiche :
- id, start (parsed), finish (parsed)
- full_name, email (si présent), téléphone (si présent)
- raw object complet pour debug

Assure-toi d'avoir les variables d'env SSS_API_ACCOUNT_NAME et SSS_API_KEY
dans un fichier .env ou dans ton environnement.
"""

import datetime
import json
from dotenv import load_dotenv
from dateutil import parser
from SuperSaaS import Client, Error

# Charger variables d'environnement depuis .env (SSS_API_ACCOUNT_NAME, SSS_API_KEY)
load_dotenv()

# --- CONFIGURATION ---
SCHEDULE_ID = 792729  # <-- Remplace par ton schedule id réel
FETCH_LIMIT = 200     # nombre max d'appels retournés par la requête (ajuste si besoin)

# --- Helpers ---
def safe_parse_datetime(value):
    """Convertit value en datetime.datetime, ou retourne None si impossible."""
    if value is None:
        return None
    if isinstance(value, datetime.datetime):
        return value
    try:
        return parser.parse(value)
    except Exception:
        return None

def extract_phone_from_appt(appt_dict):
    """
    Tente d'extraire un numéro de téléphone depuis le dict retourné par l'API.
    Cherche dans plusieurs clés courantes : 'mobile', 'phone', 'telephone', etc.
    """
    if not isinstance(appt_dict, dict):
        return None

    # clés probables (séquentiel)
    candidates = [
        "mobile",
        "phone",
        "telephone",
        "tel",
        "contact",
    ]

    for key in candidates:
        val = appt_dict.get(key)
        if val:
            # éviter les strings vides
            if isinstance(val, str) and val.strip():
                return val.strip()
            if val is not None:
                return val

    # parfois les champs personnalisés peuvent être sous 'custom' ou 'fields'
    # on tente aussi de scanner ces objets si présents
    for container_key in ("custom", "fields", "answers"):
        container = appt_dict.get(container_key)
        if isinstance(container, dict):
            for subk, subv in container.items():
                if isinstance(subv, str) and subv.strip():
                    # heuristique : si la valeur ressemble à un numéro (chiffres + +)
                    if any(ch.isdigit() for ch in subv):
                        return subv.strip()
    return None

# --- Main ---
def fetch_tomorrows_appointments():
    client = Client.instance()
    print("SuperSaaS client initialized.")

    # calculer la plage pour "demain"
    now = datetime.datetime.now()
    tomorrow_start = (now + datetime.timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
    day_after_start = tomorrow_start + datetime.timedelta(days=1)

    print(f"Fetching appointments for tomorrow: {tomorrow_start.date()} "
          f"({tomorrow_start.isoformat()} -> {day_after_start.isoformat()})\n")

    try:
        # On demande les rendez-vous en partant de tomorrow_start (filtrage supplémentaire localement)
        appointments = client.appointments.list(
            schedule_id=SCHEDULE_ID,
            start_time=tomorrow_start,
            limit=FETCH_LIMIT
        )

        if not appointments:
            print("Aucune appointment retournée par l'API.")
            return

        tomorrows = []

        for appt in appointments:
            # récupération safe des raw fields
            start_raw = getattr(appt, "start", None)
            finish_raw = getattr(appt, "finish", None)

            start_dt = safe_parse_datetime(start_raw)
            finish_dt = safe_parse_datetime(finish_raw)

            # on considère "demain" uniquement si on a pu parser start_dt
            if not start_dt:
                # skip ou log si tu veux
                continue

            if not (tomorrow_start <= start_dt < day_after_start):
                # ne prend que les rdv strictement dans la fenêtre "demain"
                continue

            # try to get values directly from object or fallback to raw dict
            try:
                appt_dict = appt.__dict__.copy()
            except Exception:
                appt_dict = {}

            phone = getattr(appt, "mobile", None) or getattr(appt, "phone", None) or extract_phone_from_appt(appt_dict)
            email = getattr(appt, "email", None)

            entry = {
                "id": getattr(appt, "id", None),
                "start_raw": start_raw,
                "start_parsed": start_dt.isoformat(),
                "finish_raw": finish_raw,
                "finish_parsed": finish_dt.isoformat() if finish_dt else None,
                "full_name": getattr(appt, "full_name", None),
                "email": email,
                "phone": phone,
                "raw_object": appt_dict
            }

            tomorrows.append(entry)

        if not tomorrows:
            print("Aucun rendez-vous trouvé pour demain (après parsing).")
            return

        print(f"Found {len(tomorrows)} appointment(s) for tomorrow:\n")
        for appt in tomorrows:
            # affichage lisible
            print(json.dumps({
                "id": appt["id"],
                "start": appt["start_parsed"],
                "finish": appt["finish_parsed"],
                "full_name": appt["full_name"],
                "email": appt["email"] or "N/A",
                "phone": appt["phone"] or "N/A"
            }, indent=2, ensure_ascii=False))
            # si tu veux voir le raw object complet, décommente la ligne suivante:
            # print("RAW:", json.dumps(appt["raw_object"], indent=2, default=str, ensure_ascii=False))
            print("-" * 60)

    except Error as e:
        print(f"\nAn API error occurred: {e}")
    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}")

if __name__ == "__main__":
    fetch_tomorrows_appointments()
