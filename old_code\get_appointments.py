# get_appointments_debug.py
import datetime
import json
from dotenv import load_dotenv
from dateutil import parser
from SuperSaaS import Client, Error

# Load variables from .env (SSS_API_ACCOUNT_NAME and SSS_API_KEY)
load_dotenv()

# --- Configuration ---
SCHEDULE_ID = 792729  # <--- REMPLACE AVEC TON SCHEDULE ID

def safe_parse_datetime(value):
    """Parse a datetime-like value into a datetime.datetime or return None."""
    if value is None:
        return None
    if isinstance(value, datetime.datetime):
        return value
    try:
        # parser.parse gère la plupart des formats (ISO, "YYYY-MM-DD HH:MM:SS", etc.)
        return parser.parse(value)
    except Exception:
        return None

def fetch_todays_appointments():
    client = Client.instance()
    print("SuperSaaS client initialized.")

    # définir la plage "aujourd'hui"
    today_start = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    tomorrow_start = today_start + datetime.timedelta(days=1)

    print(f"Fetching appointments from {today_start.strftime('%Y-%m-%d %H:%M:%S')}...")

    try:
        appointments = client.appointments.list(
            schedule_id=SCHEDULE_ID,
            start_time=today_start,
            limit=200
        )

        if not appointments:
            print("\nNo appointments returned by the API.")
            return

        found_today = []

        for appt in appointments:
            # read raw fields safely
            start_raw = getattr(appt, "start", None)
            finish_raw = getattr(appt, "finish", None)

            start_dt = safe_parse_datetime(start_raw)
            finish_dt = safe_parse_datetime(finish_raw)

            # determine if the appointment is today (only if parsed)
            is_today = False
            if start_dt:
                # comparaison entre datetime objets uniquement
                if today_start <= start_dt < tomorrow_start:
                    is_today = True

            # build a dict to dump for debugging
            try:
                appt_dict = appt.__dict__.copy()
            except Exception:
                # fallback si l'objet ne propose pas __dict__
                appt_dict = {"repr": str(appt)}

            debug_entry = {
                "id": getattr(appt, "id", None),
                "start_raw": start_raw,
                "start_parsed": start_dt.isoformat() if start_dt else None,
                "finish_raw": finish_raw,
                "finish_parsed": finish_dt.isoformat() if finish_dt else None,
                "full_name": getattr(appt, "full_name", None),
                "email": getattr(appt, "email", None),
                # Ne supposons PAS qu'il existe un champ 'phone' : on l'affiche depuis appt_dict si présent
                "phone (raw if present)": appt_dict.get("phone") if isinstance(appt_dict, dict) else None,
                "raw_object": appt_dict,
                "is_today": is_today
            }

            if is_today:
                found_today.append(debug_entry)

        if not found_today:
            print("\nNo appointments found for today (after parsing).")
        else:
            print(f"\nFound {len(found_today)} appointment(s) for today:\n")
            for entry in found_today:
                # pretty print each appointment's debug dict
                print(json.dumps(entry, indent=2, default=str))
                print("-" * 60)

    except Error as e:
        print(f"\nAn API error occurred: {e}")
    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}")

if __name__ == "__main__":
    fetch_todays_appointments()
