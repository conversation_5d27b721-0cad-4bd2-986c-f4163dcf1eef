# supersaas_api.py

import datetime
from dotenv import load_dotenv
from dateutil import parser
from SuperSaaS import Client, Error

load_dotenv()

SCHEDULE_ID = 792729  # <-- Remplace par ton schedule id réel
FETCH_LIMIT = 200     # nombre max d'appels retournés par la requête

def safe_parse_datetime(value):
    """Convertit value en datetime.datetime, ou retourne None si impossible."""
    if value is None:
        return None
    if isinstance(value, datetime.datetime):
        return value
    try:
        return parser.parse(value)
    except Exception:
        return None

def extract_phone_from_appt(appt_dict):
    """
    Tente d'extraire un numéro de téléphone depuis le dict retourné par l'API.
    Cherche dans plusieurs clés courantes : 'mobile', 'phone', 'telephone', etc.
    """
    if not isinstance(appt_dict, dict):
        return None

    # clés probables (séquentiel)
    candidates = [
        "mobile",
        "phone",
        "telephone",
        "tel",
        "contact",
    ]

    for key in candidates:
        val = appt_dict.get(key)
        if val:
            # éviter les strings vides
            if isinstance(val, str) and val.strip():
                return val.strip()
            if val is not None:
                return val

    # parfois les champs personnalisés peuvent être sous 'custom' ou 'fields'
    # on tente aussi de scanner ces objets si présents
    for container_key in ("custom", "fields", "answers"):
        container = appt_dict.get(container_key)
        if isinstance(container, dict):
            for subk, subv in container.items():
                if isinstance(subv, str) and subv.strip():
                    # heuristique : si la valeur ressemble à un numéro (chiffres + +)
                    if any(ch.isdigit() for ch in subv):
                        return subv.strip()
    return None

def get_appointments_for_day(target_date):
    """
    Fetches all appointments from SuperSaaS for a specific date.
    Based on the working get_tomorrows_appointments.py implementation.
    """
    client = Client.instance()
    
    # calculer la plage pour le jour cible
    target_start = datetime.datetime.combine(target_date, datetime.time.min)
    day_after_start = target_start + datetime.timedelta(days=1)

    print(f"Fetching appointments for {target_date.strftime('%Y-%m-%d')}...")

    try:
        # On demande les rendez-vous en partant de target_start
        appointments = client.appointments.list(
            schedule_id=SCHEDULE_ID,
            start_time=target_start,
            limit=FETCH_LIMIT
        )

        if not appointments:
            return []

        target_appointments = []

        for appt in appointments:
            # récupération safe des raw fields
            start_raw = getattr(appt, "start", None)
            start_dt = safe_parse_datetime(start_raw)

            # on considère le jour cible uniquement si on a pu parser start_dt
            if not start_dt:
                continue

            if not (target_start <= start_dt < day_after_start):
                # ne prend que les rdv strictement dans la fenêtre du jour cible
                continue

            # try to get values directly from object or fallback to raw dict
            try:
                appt_dict = appt.__dict__.copy()
            except Exception:
                appt_dict = {}

            phone = getattr(appt, "mobile", None) or getattr(appt, "phone", None) or extract_phone_from_appt(appt_dict)

            # Add parsed datetime and phone as attributes for easy access
            appt.start = start_dt  # Replace string with parsed datetime
            appt.phone = phone
            target_appointments.append(appt)

        return target_appointments

    except Error as e:
        print(f"An API error occurred: {e}")
        return [] # Return an empty list on error
